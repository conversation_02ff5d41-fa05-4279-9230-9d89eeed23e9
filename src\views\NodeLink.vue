<template>
  <div class="node-link-container">
    <div :id="containerId" ref="diagramContainer">
      <!-- 渲染节点 -->
      <div v-for="node in nodes" 
           :key="node.id" 
           :id="node.id" 
           class="node-item"
           :style="{ left: node.left + 'px', top: node.top + 'px' }">
        {{ node.name }}
      </div>
    </div>
    
    <!-- 使用teleport将tooltip挂载到body上，解决jsPlumb中z-index问题 -->
    <teleport to="body">
      <div class="tooltip-container"></div>
    </teleport>
  </div>
</template>

<script>
import { ref, onMounted, reactive, nextTick, watch, onBeforeUnmount } from 'vue';
import { ElTooltip } from 'element-plus';
import { createVNode, render } from 'vue';
import { jsPlumb } from 'jsplumb';

export default {
  name: 'NodeLink',
  props: {
    containerId: {
      type: String,
      default: 'diagramContainer'
    }
  },
  
  setup(props, { emit }) {
    // jsPlumb实例
    const jsPlumbInstance = ref(null);
    const diagramContainer = ref(null);
    
    // 节点数据
    const nodes = reactive([
      { id: 'node1', name: '节点1', left: 50, top: 50 },
      { id: 'node2', name: '节点2', left: 250, top: 50 },
      { id: 'node3', name: '节点3', left: 50, top: 200 },
      { id: 'node4', name: '节点4', left: 250, top: 200 }
    ]);
    
    // 连线数据
    const connections = reactive([
      {
        id: 'conn1',
        source: 'node1',
        target: 'node2',
        label: '关联关系',
        tooltipContent: '这是关联关系的详细说明'
      },
      {
        id: 'conn2',
        source: 'node1',
        target: 'node3',
        label: '包含关系',
        tooltipContent: '节点1包含节点3'
      },
      {
        id: 'conn3',
        source: 'node2',
        target: 'node4',
        label: '引用关系',
        tooltipContent: '节点2引用节点4'
      },
      {
        id: 'conn4',
        source: 'node3',
        target: 'node4',
        label: '依赖关系',
        tooltipContent: '节点3依赖节点4'
      }
    ]);

    // 连接线样式
    const connectorSettings = reactive({
      connector: ['Bezier', { curviness: 50 }],
      paintStyle: { strokeWidth: 2, stroke: '#5c6bc0', outlineStroke: 'transparent', outlineWidth: 4 },
      endpointStyle: { fill: '#5c6bc0', radius: 5 },
      hoverPaintStyle: { stroke: '#1879FF' },
      maxConnections: -1 // 无限连接
    });
    
    // 创建tooltip VNode并挂载到DOM
    const tooltipContentRefs = new Map();

    const createTooltip = (labelElement, tooltipContentRef) => {
      const originalLabelContent = labelElement.innerHTML;
      labelElement.innerHTML = '';
      
      // Create tooltip's VNode
      const tooltipVNode = createVNode(ElTooltip, {
        content: tooltipContentRef.value,
        effect: 'dark',
        placement: 'top',
        popperClass: 'jsplumb-tooltip'
      }, {
        // Default slot content is the original label
        default: () => originalLabelContent
      });
      
      // Render VNode to the labelElement directly
      render(tooltipVNode, labelElement);
    };
    
    // 初始化jsPlumb
    const initializeJsPlumb = () => {
      console.log('Initializing jsPlumb for container:', props.containerId);
      // Old version jsPlumb
      jsPlumbInstance.value = jsPlumb.getInstance({
        Container: document.getElementById(props.containerId), // Use the prop here
        ConnectionsDetachable: false
      });
      
      // Ensure reset first
      if (jsPlumbInstance.value) {
        jsPlumbInstance.value.reset();
      }
      
      // Wait for jsPlumb to be ready
      jsPlumbInstance.value.ready(function() {
        nextTick(() => {
          // Create connections
          createConnections();
          jsPlumbInstance.value.repaintEverything(); // Ensure everything is painted after connections
        });
      });
    };
    
    const createConnections = () => {
      connections.forEach(conn => {
        // 检查元素是否存在
        const sourceEl = document.getElementById(conn.source);
        const targetEl = document.getElementById(conn.target);
        
        if (!sourceEl || !targetEl) {
          console.error(`Elements not found: ${conn.source} or ${conn.target}`);
          return;
        }
        
        console.log('Creating connection between', conn.source, 'and', conn.target);
        
        const connection = jsPlumbInstance.value.connect({
          source: conn.source,
          target: conn.target,
          overlays: [
            ['Arrow', { width: 10, length: 10, location: 1 }],
            ['Label', {
              label: conn.label,
              id: `label-${conn.id}`,
              cssClass: 'connection-label' + (conn.tooltipContent ? ' connection-label-has-tooltip' : '')
            }]
          ]
        }, connectorSettings);
        
        // 记录连接对象供调试使用
        console.log('Connection created:', connection);
        
        // If tooltip content exists, apply the tooltip
        if (conn.tooltipContent) {
          const tooltipRef = ref(conn.tooltipContent);
          tooltipContentRefs.set(conn.id, tooltipRef);

          nextTick(() => {
            const labelOverlay = connection.getOverlay(`label-${conn.id}`);
            if (labelOverlay && labelOverlay.getElement()) {
              const labelElement = labelOverlay.getElement();
              labelElement.dataset.description = conn.tooltipContent;
              createTooltip(labelElement, tooltipRef);
            }
          });
        }
      });
    };
    
    // In mounted, ensure DOM is ready then emit event
    onMounted(() => {
      nextTick(() => {
        emit('diagram-ready');
      });
    });
    
    onBeforeUnmount(() => {
      if (jsPlumbInstance.value) {
        jsPlumbInstance.value.reset(); // Clean up jsPlumb instance when component is unmounted
        console.log('jsPlumb instance reset for container:', props.containerId);
      }
    });
    
    watch(() => connections, (newConnections, oldConnections) => {
      newConnections.forEach(newConn => {
        const oldConn = oldConnections.find(oc => oc.id === newConn.id);
        if (oldConn && oldConn.tooltipContent !== newConn.tooltipContent) {
          const tooltipRef = tooltipContentRefs.get(newConn.id);
          if (tooltipRef) {
            tooltipRef.value = newConn.tooltipContent;
          }
        }
      });
    }, { deep: true });
    
    return {
      diagramContainer,
      nodes,
      connections
    };
  }
}
</script>

<style scoped>
.node-link-container {
  position: relative;
  width: 100%;
  height: 100%;
}

#diagramContainer {
  width: 100%;
  height: 500px;
  position: relative;
}

.node-item {
  position: absolute;
  width: 120px;
  height: 60px;
  border: 1px solid #5c6bc0;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  cursor: pointer;
}

:deep(.connection-label) {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  z-index: 10;
}

:deep(.jsplumb-tooltip) {
  z-index: 9999;
}

:deep(.connection-label-has-tooltip) {
  cursor: pointer;
}
</style>
