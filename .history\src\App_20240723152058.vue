<script setup>
import { ref } from "vue";
import HelloWorld from "./components/HelloWorld.vue";
let tooltipText = {
  content: "21312o3u12938u120930912308912",
  placement: "top",
};
const a = 12321321;
console.log(a);
console.log(tooltipText.value);
</script>

<template>
  <div>
    <a href="https://vitejs.dev" target="_blank">
      <img src="/vite.svg" class="logo" alt="Vite logo" />
    </a>
    <a href="https://vuejs.org/" target="_blank">
      <img src="./assets/vue.svg" class="logo vue" alt="Vue logo" />
    </a>
  </div>
  <h1 class="text-3xl font-bold underline">Hello world!111</h1>
  <HelloWorld msg="Vite + Vue" />
  <el-button v-custom-tooltip="tooltipText">Hover me</el-button>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
