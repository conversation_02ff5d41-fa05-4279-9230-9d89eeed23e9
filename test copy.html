<!DOCTYPE html>
<html lang="en">

<head>
    <title></title>
    <style>
        body {
            display: block;
            margin: 8px;
        }

        #diagramContainer {
            padding: 20px;
            width: 80%;
            height: 200px;
            border: 1px solid gray;
        }

        .item {
            height: 80px;
            width: 80px;
            border: 1px solid blue;
            position: absolute;
        }

        /* Add tooltip style */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            border-radius: 4px;
            padding: 5px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>

<body>
    <div id="diagramContainer">
        <div id="item_left" class="item"></div>
        <div id="item_right" class="item" style="left:250px;"></div>
    </div>
</body>

</html>
<script src="./jsplumb.min.js"></script>
<script>
    /* global jsPlumb */
    jsPlumb.ready(function () {
        var common = {
            endpoint: 'Rectangle',
            connector: ['Bezier'],
            anchor: ['Left', 'Right']
        }

        // Connect the elements with tooltip in the label
        var connection = jsPlumb.connect({
            source: 'item_left',
            target: 'item_right',
            paintStyle: { stroke: 'lightgray', strokeWidth: 3 },
            endpointStyle: { fill: 'red', outlineStroke: 'darkgray', outlineWidth: 2 },
            overlays: [
                ['Arrow', { width: 12, length: 12, location: 0.5 }],
                ["Label", {
                    label: "<div class='tooltip'><span>关联2</span><span style='color: red'>2323</span><span class='tooltiptext'>这里是提示内容</span></div>",
                    cssClass: "csslabel"
                }]
            ]
        }, common);

        // Alternative method: Adding tooltip to existing labels after connection is created
        // setTimeout(function() {
        //     var labels = document.querySelectorAll(".csslabel");
        //     labels.forEach(function(label) {
        //         // Create tooltip wrapper
        //         var content = label.innerHTML;
        //         var tooltip = document.createElement("div");
        //         tooltip.className = "tooltip";
        //         tooltip.innerHTML = content + "<span class='tooltiptext'>这里是提示内容</span>";
        //         label.innerHTML = "";
        //         label.appendChild(tooltip);
        //     });
        // }, 100);
    });

    // 创建一个匹配rgba的正则表达式
    let originalString = "rgba(255, 0, 0, 1)"
    const regex = /rgba\((\d+,\s*\d+,\s*\d+,\s*)[0-9.]+\)/g;

    // 替换匹配到的rgba中的a为新值，例如0.5
    const modifiedString = 'rgba(' + originalString.replace(regex, '$1' + '0' + ')');
    console.log(modifiedString); // 输出：rgba(255, 0, 0, 0.5)

    // jsPlumb.ready(function () {
    //     jsPlumb.setContainer('diagramContainer')

    //     var common = {
    //         isSource: true,
    //         isTarget: true,
    //         connector: ['Straight'],
    //         endpoint: 'Dot',
    //         paintStyle: {
    //         fill: 'white',
    //         outlineStroke: 'blue',
    //         strokeWidth: 3
    //         },
    //         connectorStyle: {
    //             outlineStroke: 'green',
    //             strokeWidth: 1
    //         },
    //         connectorHoverStyle: {
    //             strokeWidth: 2
    //         }
    //     }

    //     jsPlumb.addEndpoint('item_left', {
    //         anchors: ['Right']
    //     }, common)

    //     jsPlumb.addEndpoint('item_right', {
    //         anchor: 'Left'
    //     }, common)

    //     jsPlumb.addEndpoint('item_right', {
    //         anchor: 'Right'
    //     }, common)
    // })
    function changeRGBATransparency(rgbaStr, transparency) {
        // 将RGBA颜色值转换为数值
        let rgba = rgbaStr.match(/\d+/g).map(Number);

        // 修改透明度
        rgba[3] = transparency;

        // 将数值转换回RGBA颜色值
        return `rgba(${rgba[0]}, ${rgba[1]}, ${rgba[2]}, ${rgba[3]})`;
    }

    console.log(changeRGBATransparency("rgba(255, 87, 51, 0.5)", 0.3)); // 输出 "rgba(255, 87, 51, 0.3)"
    // 输出 "rgba(255, 87, 51, 0.3)"

</script>