<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PETS三级笔试65天备考计划 - 在职版</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            color: #333;
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        header {
            background: linear-gradient(120deg, #1a5fb4, #2c8bd9);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }
        
        header::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            transform: rotate(30deg);
        }
        
        .header-content {
            position: relative;
            z-index: 2;
        }
        
        h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .subtitle {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .stats {
            display: flex;
            gap: 25px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 15px 25px;
            border-radius: 10px;
            min-width: 200px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-card i {
            font-size: 1.8rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
        }
        
        h2 {
            color: #1a5fb4;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eaeaea;
            display: flex;
            align-items: center;
        }
        
        h2 i {
            margin-right: 12px;
            background: #e4edf5;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #1a5fb4;
        }
        
        .phase-container {
            display: flex;
            gap: 25px;
            flex-wrap: wrap;
        }
        
        .phase {
            flex: 1;
            min-width: 300px;
            border-left: 4px solid #1a5fb4;
            padding-left: 20px;
            position: relative;
        }
        
        .phase::before {
            content: "";
            position: absolute;
            left: -12px;
            top: 0;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #1a5fb4;
        }
        
        .phase h3 {
            font-size: 1.6rem;
            color: #1a5fb4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .phase h3 span {
            background: #e4edf5;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 12px;
        }
        
        .phase ul {
            padding-left: 25px;
            margin-bottom: 20px;
        }
        
        .phase li {
            margin-bottom: 12px;
            position: relative;
        }
        
        .phase li::before {
            content: "•";
            color: #1a5fb4;
            font-weight: bold;
            position: absolute;
            left: -20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        th {
            background: #1a5fb4;
            color: white;
            text-align: left;
            padding: 15px;
        }
        
        td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        tr:nth-child(even) {
            background-color: #f9fbfd;
        }
        
        tr:hover {
            background-color: #edf5ff;
        }
        
        .materials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .material-card {
            background: #f0f7ff;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #d0e3ff;
            transition: all 0.3s ease;
        }
        
        .material-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
            border-color: #a5c8ff;
        }
        
        .material-card h4 {
            color: #1a5fb4;
            margin-bottom: 12px;
            font-size: 1.2rem;
        }
        
        .material-card ul {
            padding-left: 20px;
        }
        
        .material-card li {
            margin-bottom: 8px;
            color: #555;
        }
        
        .priority {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-top: 10px;
        }
        
        .high-priority {
            background: #ffebee;
            color: #c62828;
        }
        
        .medium-priority {
            background: #fff8e1;
            color: #f57f17;
        }
        
        .daily-schedule {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .time-block {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border-left: 4px solid #1a5fb4;
        }
        
        .time-title {
            font-weight: bold;
            color: #1a5fb4;
            margin-bottom: 10px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }
        
        .time-title i {
            margin-right: 10px;
        }
        
        .action-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            flex: 1;
            min-width: 250px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border-top: 4px solid #1a5fb4;
        }
        
        .action-card h3 {
            color: #1a5fb4;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .action-card h3 i {
            margin-right: 10px;
        }
        
        .progress-container {
            margin: 40px 0;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
        }
        
        .progress-bar {
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            margin: 20px 0;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1a5fb4, #2c8bd9);
            border-radius: 15px;
            width: 0%;
            transition: width 1s ease;
        }
        
        .progress-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .btn-container {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 14px 28px;
            border-radius: 8px;
            border: none;
            font-weight: bold;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn i {
            margin-right: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(120deg, #1a5fb4, #2c8bd9);
            color: white;
            box-shadow: 0 4px 12px rgba(26, 95, 180, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(26, 95, 180, 0.4);
        }
        
        .btn-secondary {
            background: white;
            color: #1a5fb4;
            border: 2px solid #1a5fb4;
        }
        
        .btn-secondary:hover {
            background: #f0f7ff;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .tip {
            background: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            border-radius: 0 8px 8px 0;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2.2rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
            
            .phase-container {
                flex-direction: column;
            }
            
            .daily-schedule {
                grid-template-columns: 1fr;
            }
        }
        
        @media print {
            .btn-container, .stats, .tip {
                display: none;
            }
            
            body {
                background: white;
                padding: 10px;
            }
            
            .card {
                box-shadow: none;
                padding: 15px;
            }
            
            header {
                background: #1a5fb4;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1><i class="fas fa-book-open"></i> PETS三级笔试65天备考计划</h1>
                <p class="subtitle">在职备考方案 - 每天9:00-20:00上班也能高效学习</p>
                
                <div class="stats">
                    <div class="stat-card">
                        <i class="fas fa-user-clock"></i>
                        <h3>65天</h3>
                        <p>备考总时长</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-briefcase"></i>
                        <h3>3小时/天</h3>
                        <p>每日学习时间</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-bolt"></i>
                        <h3>碎片化</h3>
                        <p>学习策略</p>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-trophy"></i>
                        <h3>60分+</h3>
                        <p>目标分数</p>
                    </div>
                </div>
            </div>
        </header>
        
        <div class="progress-container">
            <h2><i class="fas fa-chart-line"></i> 备考进度追踪</h2>
            <p>当前计划执行进度：<strong>0/65天</strong></p>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="progress-labels">
                <span>第1天</span>
                <span>第33天</span>
                <span>第65天</span>
            </div>
            <button class="btn btn-secondary" onclick="updateProgress(5)"><i class="fas fa-plus"></i> 更新进度</button>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-book"></i> 必备备考资料</h2>
            <div class="materials-grid">
                <div class="material-card">
                    <h4>核心资料 <span class="priority high-priority">必备</span></h4>
                    <ul>
                        <li>最新PETS三级官方大纲</li>
                        <li>近5年真题及详解（含听力音频）</li>
                        <li>专项练习册（阅读/完型/语法填空）</li>
                    </ul>
                </div>
                
                <div class="material-card">
                    <h4>词汇工具 <span class="priority high-priority">必备</span></h4>
                    <ul>
                        <li>PETS三级核心词汇书/APP</li>
                        <li>真题生词本（自制）</li>
                        <li>高频短语手册</li>
                    </ul>
                </div>
                
                <div class="material-card">
                    <h4>听力素材 <span class="priority high-priority">必备</span></h4>
                    <ul>
                        <li>真题听力材料</li>
                        <li>BBC Learning English</li>
                        <li>VOA慢速英语</li>
                    </ul>
                </div>
                
                <div class="material-card">
                    <h4>写作工具 <span class="priority medium-priority">推荐</span></h4>
                    <ul>
                        <li>真题范文集</li>
                        <li>书信/短文模板库</li>
                        <li>Grammarly写作批改工具</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-calendar-alt"></i> 分阶段备考计划</h2>
            <div class="phase-container">
                <div class="phase">
                    <h3>基础构建阶段 <span>第1-15天</span></h3>
                    <p><strong>目标：</strong> 熟悉题型、诊断弱项、词汇语法筑基</p>
                    <ul>
                        <li><strong>第1天：</strong> 限时模考1套 → 标记弱项</li>
                        <li><strong>词汇：</strong> 早晚通勤+午休背词50个/天</li>
                        <li><strong>听力：</strong> 每日30分钟精听真题（听写+跟读）</li>
                        <li><strong>阅读：</strong> 每日2篇真题（限时15分钟/篇）</li>
                        <li><strong>语法：</strong> 每日1个高频语法点复习</li>
                        <li><strong>周末：</strong> 整理错题本 + 写作范文拆解</li>
                    </ul>
                </div>
                
                <div class="phase">
                    <h3>专项突破阶段 <span>第16-45天</span></h3>
                    <p><strong>目标：</strong> 技巧强化、写作实战、全真模考</p>
                    <ul>
                        <li><strong>听力：</strong> 1.2倍速精听 + 专项训练</li>
                        <li><strong>阅读：</strong> 每日3篇（限时40分钟）</li>
                        <li><strong>写作：</strong> 每周2篇（书信+短文）</li>
                        <li><strong>语言知识：</strong> 每日1篇完型填空</li>
                        <li><strong>核心任务：</strong> 每周日全真模考 + 深度分析</li>
                    </ul>
                </div>
                
                <div class="phase">
                    <h3>冲刺优化阶段 <span>第46-65天</span></h3>
                    <p><strong>目标：</strong> 限时模考、错题清零、心态调整</p>
                    <ul>
                        <li><strong>高频模考：</strong> 第46/53/60天最新真题模考</li>
                        <li><strong>错题攻坚：</strong> 每日回顾错题本</li>
                        <li><strong>写作押题：</strong> 背诵模板 + 高频话题范文</li>
                        <li><strong>词汇保温：</strong> 速过核心词表</li>
                        <li><strong>最后7天：</strong> 停做新题！聚焦错题/模板/高频词</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-clock"></i> 每日时间表示例（工作日）</h2>
            <div class="daily-schedule">
                <div class="time-block">
                    <div class="time-title"><i class="fas fa-sun"></i> 早上 (6:30-8:30)</div>
                    <ul>
                        <li>6:30-7:00：起床洗漱</li>
                        <li>7:00-7:30：精听真题</li>
                        <li>7:30-8:00：背核心词汇</li>
                        <li>8:00-8:30：通勤路上背单词/泛听</li>
                    </ul>
                </div>
                
                <div class="time-block">
                    <div class="time-title"><i class="fas fa-cloud-sun"></i> 午休 (12:30-13:00)</div>
                    <ul>
                        <li>速做1篇阅读/完型</li>
                        <li>复习上午单词</li>
                        <li>听力泛听练习</li>
                    </ul>
                </div>
                
                <div class="time-block">
                    <div class="time-title"><i class="fas fa-moon"></i> 晚上 (20:30-23:00)</div>
                    <ul>
                        <li>20:30-21:00：语法/语言知识练习</li>
                        <li>21:00-21:30：听力精听分析</li>
                        <li>21:30-22:15：阅读训练（1-2篇）</li>
                        <li>22:15-22:30：当日复习+生词记录</li>
                        <li>22:30-23:00：洗漱休息</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-lightbulb"></i> 关键执行策略</h2>
            <div class="tip">
                <i class="fas fa-exclamation-circle"></i> 在职备考核心原则：效率至上！充分利用碎片时间，保证学习质量而非数量
            </div>
            
            <table>
                <tr>
                    <th>策略</th>
                    <th>具体方法</th>
                    <th>预期效果</th>
                </tr>
                <tr>
                    <td><strong>真题为王</strong></td>
                    <td>所有训练围绕真题展开，模考后分析时间＞做题时间</td>
                    <td>精准把握考点，熟悉考试模式</td>
                </tr>
                <tr>
                    <td><strong>碎片时间利用</strong></td>
                    <td>通勤2×30min + 午休30min + 工作间隙20min → 背词/泛听</td>
                    <td>每日额外获得1.5小时学习时间</td>
                </tr>
                <tr>
                    <td><strong>写作提分捷径</strong></td>
                    <td>模板句(20%) + 真题范文仿写(50%) + 批改反馈(30%)</td>
                    <td>短期内提升5-8分</td>
                </tr>
                <tr>
                    <td><strong>错题本制度</strong></td>
                    <td>按题型分类记录错误，每周回顾</td>
                    <td>避免重复犯错，效率提升40%</td>
                </tr>
                <tr>
                    <td><strong>健康管理</strong></td>
                    <td>每日睡眠≥6.5小时，每周运动2次</td>
                    <td>保持精力充沛，提高学习效率</td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2><i class="fas fa-tasks"></i> 立即行动清单</h2>
            <div class="action-list">
                <div class="action-card">
                    <h3><i class="fas fa-shopping-cart"></i> 资料准备</h3>
                    <ul>
                        <li>购买最新版真题集（含听力）</li>
                        <li>下载单词APP设定每日任务</li>
                        <li>准备错题本（纸质/电子）</li>
                    </ul>
                </div>
                
                <div class="action-card">
                    <h3><i class="fas fa-calendar-check"></i> 计划制定</h3>
                    <ul>
                        <li>标记日历：每周日为模考日</li>
                        <li>设定阶段目标（基础/强化/冲刺）</li>
                        <li>安排每日固定学习时段</li>
                    </ul>
                </div>
                
                <div class="action-card">
                    <h3><i class="fas fa-user-cog"></i> 环境准备</h3>
                    <ul>
                        <li>创建专用学习空间</li>
                        <li>安装学习APP（Grammarly等）</li>
                        <li>准备降噪耳机用于听力练习</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="btn-container">
            <button class="btn btn-primary" onclick="window.print()"><i class="fas fa-print"></i> 打印完整计划</button>
            <button class="btn btn-secondary"><i class="fas fa-file-download"></i> 下载PDF版本</button>
            <button class="btn btn-secondary"><i class="fas fa-calendar-plus"></i> 添加到日历</button>
        </div>
        
        <footer>
            <p>PET三级笔试备考计划 | 在职专用版 | 设计：学习策略中心</p>
            <p>© 2023 高效学习计划 | 65天冲刺成功！</p>
        </footer>
    </div>
    
    <script>
        // 模拟进度更新功能
        function updateProgress(days) {
            const progressBar = document.querySelector('.progress-fill');
            const currentWidth = parseFloat(progressBar.style.width) || 0;
            const newWidth = Math.min(currentWidth + (days/65)*100, 100);
            
            progressBar.style.width = newWidth + '%';
            
            // 更新进度文本
            const currentDays = Math.round((newWidth/100)*65);
            document.querySelector('.progress-container p').innerHTML = 
                `当前计划执行进度：<strong>${currentDays}/65天</strong>`;
            
            // 提示信息
            alert(`成功更新进度！已坚持 ${days} 天，继续加油！`);
        }
        
        // 初始加载时设置进度为0%
        window.onload = function() {
            document.querySelector('.progress-fill').style.width = '0%';
        };
    </script>
</body>
</html>