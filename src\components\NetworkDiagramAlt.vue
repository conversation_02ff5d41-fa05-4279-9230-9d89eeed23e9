<template>
  <div class="network-diagram-container">
    <div ref="canvasRef" class="canvas-container"></div>
    
    <!-- 使用 MiniviewComponent 组件，确保其父容器有正确的定位 -->
    <div class="miniview-wrapper">
      <MiniviewComponent 
        v-if="jsPlumbInstance" 
        :jsPlumbInstance="jsPlumbInstance" 
        :width="200" 
        :height="150" 
        :scale="0.15" 
        :showConnectionLines="true" 
      />
    </div>
  </div>
</template>

<script setup>
// 这是一个备选组件，使用全局 jsPlumb
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import MiniviewComponent from './MiniviewComponent.vue';

const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  },
  connections: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['node-click']);
const canvasRef = ref(null);
const jsPlumbInstance = ref(null);

onMounted(async () => {
  // 在 mounted 生命周期中初始化 JsPlumb
  await nextTick();
  
  // 使用全局变量，这是最可靠的方法
  if (window.jsPlumb) {
    console.log('使用全局 jsPlumb');
    initJsPlumb(window.jsPlumb);
    return;
  } else {
    console.error('找不到 jsPlumb 全局变量，请确保在 index.html 中正确引入了 jsPlumb');
  }
  
  // 如果全局变量不存在，尝试动态导入原生 jsplumb
  try {
    console.log('尝试动态导入原生 jsplumb');
    const { jsPlumb } = await import('jsplumb');
    if (jsPlumb) {
      initJsPlumb(jsPlumb);
    } else {
      console.error('无法从 jsplumb 包中获取 jsPlumb');
    }
  } catch (error) {
    console.error('所有导入方法均失败:', error);
  }
});

onBeforeUnmount(() => {
  // 清理资源
  if (jsPlumbInstance.value) {
    jsPlumbInstance.value.reset();
    jsPlumbInstance.value = null;
  }
});

const initJsPlumb = (jsPlumb) => {
  console.log('初始化 JsPlumb', jsPlumb);
  
  // 先确保容器存在
  if (!canvasRef.value) {
    console.error('画布容器不存在');
    return;
  }
  
  // 初始化 JsPlumb 实例
  jsPlumbInstance.value = jsPlumb.getInstance({
    Container: canvasRef.value,
    ConnectionsDetachable: false,
    Connector: ['Bezier', { curviness: 50 }],
    Endpoint: ['Dot', { radius: 5 }],
    Anchor: ['Continuous'],
    PaintStyle: { 
      stroke: '#5c96bc', 
      strokeWidth: 2 
    },
    HoverPaintStyle: { 
      stroke: '#1e8151', 
      strokeWidth: 3 
    }
  });

  // 渲染节点
  renderNodes();

  // 渲染连接
  renderConnections();
};

const renderNodes = () => {
  if (!jsPlumbInstance.value || !canvasRef.value) return;
  
  props.nodes.forEach(node => {
    const nodeElement = document.createElement('div');
    nodeElement.id = node.id;
    nodeElement.className = 'node';
    nodeElement.innerHTML = node.label;
    nodeElement.style.left = `${node.x}px`;
    nodeElement.style.top = `${node.y}px`;
    
    // 添加点击事件
    nodeElement.addEventListener('click', (e) => {
      emit('node-click', node.id);
    });
    
    canvasRef.value.appendChild(nodeElement);
    
    // 将节点设置为可拖拽
    jsPlumbInstance.value.draggable(nodeElement, {
      containment: canvasRef.value
    });
    
    // 添加端点
    jsPlumbInstance.value.makeSource(nodeElement, {
      filter: '.node-endpoint',
      anchor: 'Continuous'
    });
    
    jsPlumbInstance.value.makeTarget(nodeElement, {
      anchor: 'Continuous'
    });
  });
};

const renderConnections = () => {
  if (!jsPlumbInstance.value) return;
  
  props.connections.forEach(connection => {
    jsPlumbInstance.value.connect({
      source: connection.source,
      target: connection.target,
      label: connection.label
    });
  });
};
</script>

<style scoped>
.network-diagram-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.canvas-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  background-color: #f5f5f5;
  position: relative;
}

/* 添加一个包装器，确保缩略图有正确的定位上下文 */
.miniview-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1000;
  pointer-events: none;
}

.node {
  position: absolute;
  width: 120px;
  height: 60px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 5px;
  text-align: center;
  line-height: 60px;
  cursor: pointer;
  user-select: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: box-shadow 0.3s, background-color 0.3s;
}

.node:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #f8f8f8;
}

/* 覆盖一些 jsPlumb 的默认样式 */
:deep(.jtk-overlay) {
  z-index: 11;
}

:deep(.jtk-endpoint) {
  z-index: 12;
}

:deep(.jtk-connector) {
  z-index: 9;
}
</style>