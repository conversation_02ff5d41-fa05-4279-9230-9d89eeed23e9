# Vue3 + JsPlumb 链路图与缩略图示例

这个项目展示了如何在 Vue3 中使用 JsPlumb 创建交互式链路图，并在右上角添加缩略图功能。

## 功能特点

- 使用 Vue3 组合式 API
- JsPlumb 实现可拖拽的链路图
- 右上角缩略图，可查看整体布局
- 自定义节点和连接样式

## 项目设置

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

## 使用方法

1. 在你的 Vue 组件中引入 NetworkDiagram 组件
2. 提供 nodes 和 connections 数据
3. 自定义样式或扩展功能

## 注意事项

- JsPlumb 2.x 版本与 Vue3 集成时需要注意 DOM 的挂载时机
- 缩略图功能依赖于 JsPlumb 的 MiniView 插件
- 如果需要更复杂的功能，请参考 JsPlumb 的官方文档

## 示例代码

```vue
<template>
  <NetworkDiagram :nodes="nodes" :connections="connections" />
</template>

<script>
import { ref } from 'vue';
import NetworkDiagram from './components/NetworkDiagram.vue';

export default {
  components: { NetworkDiagram },
  setup() {
    const nodes = ref([
      { id: 'node1', label: '节点1', x: 100, y: 100 },
      { id: 'node2', label: '节点2', x: 350, y: 100 }
    ]);
    
    const connections = ref([
      { source: 'node1', target: 'node2', label: '连接1' }
    ]);
    
    return { nodes, connections };
  }
};
</script>
```

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin).
