<!DOCTYPE html>
<html>

<head>
    <style>
        /* 连线样式 */
        .jtk-connector {
            position: relative;
        }

        /* 连线标签样式 */
        .label {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            padding: 4px 8px;
            background: #333;
            color: white;
            border-radius: 3px;
            font-size: 12px;
            cursor: default;
            user-select: none;
            white-space: nowrap;
        }

        /* 自定义 Tooltip */
        .custom-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 13px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 1000;
        }
    </style>
</head>

<body>
    <div id="canvas" style="position: relative; height: 500px;"></div>
    <div id="tooltip" class="custom-tooltip"></div>

    <script src="https://cdn.jsdelivr.net/npm/jsplumb@2.19.0/dist/jsplumb.min.js"></script>
    <script>
        const tooltip = document.getElementById('tooltip');
        const jsPlumb = window.jsPlumb;

        // 初始化 jsPlumb
        jsPlumb.ready(() => {
            // 创建两个节点
            const node1 = document.createElement('div');
            node1.id = 'node1';
            node1.textContent = 'Node 1';
            node1.style.position = 'absolute';
            node1.style.left = '100px';
            node1.style.top = '100px';
            node1.style.width = '100px';
            node1.style.height = '50px';
            node1.style.background = '#eee';
            node1.style.border = '1px solid #ccc';

            const node2 = document.createElement('div');
            node2.id = 'node2';
            node2.textContent = 'Node 2';
            node2.style.position = 'absolute';
            node2.style.left = '400px';
            node2.style.top = '300px';
            node2.style.width = '100px';
            node2.style.height = '50px';
            node2.style.background = '#eee';
            node2.style.border = '1px solid #ccc';

            document.getElementById('canvas').append(node1, node2);

            // 创建连接
            const conn = jsPlumb.connect({
                source: 'node1',
                target: 'node2',
                anchors: ['Right', 'Left'],
                connector: ['Straight'],
                endpoints: ['Dot', 'Rectangle'],
                paintStyle: { stroke: '#456', strokeWidth: 2 },
                hoverPaintStyle: { stroke: '#f00', strokeWidth: 3 }
            });

            // 添加带 Tooltip 的 Label
            addLabeledTooltip(conn, '重要连接');

            // 为所有现有连接添加 Label（可选）
            jsPlumb.bind('connection', (info) => {
                addLabeledTooltip(info.connection, '动态连接');
            });
        });

        function addLabeledTooltip(connection, labelText) {
            // 创建 Label 容器
            const label = document.createElement('div');
            label.className = 'label';
            label.textContent = labelText;

            // 将 Label 添加到连接
            connection.setLabel(label);

            // 创建 Tooltip
            const createTooltip = () => {
                const tooltip = document.createElement('div');
                tooltip.className = 'custom-tooltip';
                tooltip.textContent = labelText + '\n悬停查看详细信息';
                document.body.appendChild(tooltip);
                return tooltip;
            };

            // 事件绑定
            label.addEventListener('mouseenter', (e) => {
                const rect = label.getBoundingClientRect();
                tooltip.style.left = `${rect.left + rect.width / 2}px`;
                tooltip.style.top = `${rect.bottom + window.scrollY}px`;
                tooltip.style.opacity = '1';
            });

            label.addEventListener('mouseleave', () => {
                tooltip.style.opacity = '0';
                setTimeout(() => tooltip.remove(), 200);
            });

            // 首次创建 Tooltip
            if (!document.querySelector('.custom-tooltip')) {
                createTooltip();
            }
        }
    </script>
</body>

</html>