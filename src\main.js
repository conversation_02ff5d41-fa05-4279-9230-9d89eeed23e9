import "./style.css";
// main.js
import { createApp, ref, h } from "vue";
import App from "./App.vue";
import router from "./router";
import { ElTooltip, ElButton } from "element-plus";
import "element-plus/dist/index.css";

const app = createApp(App);
app.use(router)
// 注册 ElTooltip 组件
app.component(ElTooltip.name, ElTooltip);

app.directive("custom-tooltip", {
  mounted(el, binding) {
    const virtualR = ref();
    virtualR.value = el;
    //@ts-ignore
    el._t_app = createApp(ElTooltip, {
      trigger: "hover",
      virtualRef: virtualR,
      virtualTriggering: true,
      ...binding.value,
    });
    //@ts-ignore
    el._t_root = document.createElement("div");
    //@ts-ignore
    document.body.appendChild(el._t_root);
    const time = new Date().getTime();
    const id = "t_root_" + time;
    //@ts-ignore
    el._t_root.id = id;
    //@ts-ignore
    el._t_app.mount("#" + id);
  },
  updated(el, binding) {
    //@ts-ignore
    el._t_app.unmount();
    //@ts-ignore
    document.body.removeChild(el._t_root);
    const virtualR = ref();
    virtualR.value = el;
    //@ts-ignore
    el._t_app = createApp(ElTooltip, {
      trigger: "hover",
      virtualRef: virtualR,
      virtualTriggering: true,
      ...binding.value,
    });
    //@ts-ignore
    el._t_root = document.createElement("div");
    //@ts-ignore
    document.body.appendChild(el._t_root);
    const time = new Date().getTime();
    const id = "t_root_" + time;
    //@ts-ignore
    el._t_root.id = id;
    //@ts-ignore
    el._t_app.mount("#" + id);
  },
  unmounted(el) {
    el._t_app.unmount();
    document.body.removeChild(el._t_root);
  },
});

app.component("ElButton", ElButton);

app.mount("#app");
