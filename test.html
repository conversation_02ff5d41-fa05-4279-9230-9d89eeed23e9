<!DOCTYPE html>
<html lang="en">

<head>
    <title></title>
    <style>
        body {
            display: block;
            margin: 8px;
        }

        #diagramContainer {
            padding: 20px;
            width: 80%;
            height: 200px;
            border: 1px solid gray;
        }

        .item {
            height: 80px;
            width: 80px;
            border: 1px solid blue;
            position: absolute;
        }

        /* 自适应宽度的tooltip样式 */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            border-radius: 4px;
            padding: 5px 10px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;

            /* 关键自适应宽度设置 */
            white-space: nowrap;
            /* 默认不换行 */
            max-width: 520px;
            /* 最大宽度限制 */
            width: auto;
            /* 自动宽度 */
            overflow: hidden;
            /* 隐藏溢出内容 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
        }

        /* 当文字内容较多时允许换行 */
        .tooltip .tooltiptext.multiline {
            white-space: normal;
            text-align: left;
            min-width: 250px;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* 添加小箭头 */
        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }
    </style>
    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入Element Plus -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <script src="https://unpkg.com/element-plus"></script>
</head>

<body>
    <div id="app">
        <div id="diagramContainer">
            <div id="item_left" class="item"></div>
            <div id="item_right" class="item" style="left:250px;"></div>
        </div>
    </div>
</body>

</html>
<script src="./jsplumb.min.js"></script>
<script>
    /* global jsPlumb, Vue, ElementPlus */
    // 创建Vue应用
    const app = Vue.createApp({
        data() {
            return {
                tooltipContent: '这里是提示内容'
            }
        },
        mounted() {
            this.initJsPlumb();
        },
        methods: {
            initJsPlumb() {
                jsPlumb.ready(() => {
                    var common = {
                        endpoint: 'Rectangle',
                        connector: ['Bezier'],
                        anchor: ['Left', 'Right']
                    };

                    // 创建连接
                    var connection = jsPlumb.connect({
                        source: 'item_left',
                        target: 'item_right',
                        paintStyle: { stroke: 'lightgray', strokeWidth: 3 },
                        endpointStyle: { fill: 'red', outlineStroke: 'darkgray', outlineWidth: 2 },
                        overlays: [
                            ['Arrow', { width: 12, length: 12, location: 0.5 }],
                            ["Label", {
                                label: "<div class='tooltip'><span>关联2</span><span style='color: red'>2323</span><span class='tooltiptext'>这里是较短的提示内容</span></div>",
                                cssClass: "csslabel"
                            }]
                        ]
                    }, common);

                    // 在连接创建后添加Element Plus的tooltip
                    setTimeout(() => {
                        this.addElTooltips();
                    }, 100);
                });
            },
            addElTooltips() {
                const labels = document.querySelectorAll(".csslabel");
                labels.forEach((label) => {
                    // 创建el-tooltip元素
                    const elTooltip = document.createElement('el-tooltip');
                    elTooltip.setAttribute('content', this.tooltipContent);
                    elTooltip.setAttribute('effect', 'dark');
                    elTooltip.setAttribute('placement', 'top');

                    // 保存原始内容
                    const originalContent = label.innerHTML;
                    const originalLabel = label.querySelector('.jsplumb-label');

                    // 清空标签并将内容包装在el-tooltip中
                    label.innerHTML = '';
                    elTooltip.appendChild(originalLabel || document.createTextNode(originalContent));
                    label.appendChild(elTooltip);

                    // 使Vue处理el-tooltip组件
                    this.$nextTick(() => {
                        ElementPlus.ElTooltip.mounted(elTooltip);
                    });
                });
            }
        }
    });

    // 注册Element Plus
    app.use(ElementPlus);

    // 挂载Vue应用
    app.mount('#app');

    // 创建一个匹配rgba的正则表达式
    let originalString = "rgba(255, 0, 0, 1)"
    const regex = /rgba\((\d+,\s*\d+,\s*\d+,\s*)[0-9.]+\)/g;

    // 替换匹配到的rgba中的a为新值，例如0.5
    const modifiedString = 'rgba(' + originalString.replace(regex, '$1' + '0' + ')');
    console.log(modifiedString); // 输出：rgba(255, 0, 0, 0.5)

    // jsPlumb.ready(function () {
    //     jsPlumb.setContainer('diagramContainer')

    //     var common = {
    //         isSource: true,
    //         isTarget: true,
    //         connector: ['Straight'],
    //         endpoint: 'Dot',
    //         paintStyle: {
    //         fill: 'white',
    //         outlineStroke: 'blue',
    //         strokeWidth: 3
    //         },
    //         connectorStyle: {
    //             outlineStroke: 'green',
    //             strokeWidth: 1
    //         },
    //         connectorHoverStyle: {
    //             strokeWidth: 2
    //         }
    //     }

    //     jsPlumb.addEndpoint('item_left', {
    //         anchors: ['Right']
    //     }, common)

    //     jsPlumb.addEndpoint('item_right', {
    //         anchor: 'Left'
    //     }, common)

    //     jsPlumb.addEndpoint('item_right', {
    //         anchor: 'Right'
    //     }, common)
    // })
    function changeRGBATransparency(rgbaStr, transparency) {
        // 将RGBA颜色值转换为数值
        let rgba = rgbaStr.match(/\d+/g).map(Number);

        // 修改透明度
        rgba[3] = transparency;

        // 将数值转换回RGBA颜色值
        return `rgba(${rgba[0]}, ${rgba[1]}, ${rgba[2]}, ${rgba[3]})`;
    }

    console.log(changeRGBATransparency("rgba(255, 87, 51, 0.5)", 0.3)); // 输出 "rgba(255, 87, 51, 0.3)"
    // 输出 "rgba(255, 87, 51, 0.3)"

</script>