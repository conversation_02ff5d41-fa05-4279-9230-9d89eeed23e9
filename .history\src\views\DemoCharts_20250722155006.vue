<template>
  <div class="demo-charts-container">
    <h1 class="text-2xl font-bold mb-6 text-center">饼图展示效果</h1>

    <div class="charts-grid">
      <!-- 第一个饼图：在线率 -->
      <div class="chart-item">
        <div ref="onlineChart" class="chart-container"></div>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot online"></span>
            <span class="legend-text">在线率：</span>
            <span class="legend-value">90.00%</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot offline"></span>
            <span class="legend-text">离线率：</span>
            <span class="legend-value">10.00%</span>
          </div>
        </div>
      </div>

      <!-- 第二个饼图：画面正常率 -->
      <div class="chart-item">
        <div ref="normalChart" class="chart-container"></div>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot normal"></span>
            <span class="legend-text">画面正常率：</span>
            <span class="legend-value">90.00%</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot abnormal"></span>
            <span class="legend-text">画面异常率：</span>
            <span class="legend-value">10.00%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";

// 图表实例引用
const onlineChart = ref(null);
const normalChart = ref(null);
let onlineChartInstance = null;
let normalChartInstance = null;

// 在线率数据
const onlineData = [
  { value: 90, name: "在线", itemStyle: { color: "#20B2AA" } },
  { value: 10, name: "离线", itemStyle: { color: "#FFA500" } },
];

// 画面正常率数据
const normalData = [
  { value: 90, name: "正常", itemStyle: { color: "#20B2AA" } },
  { value: 10, name: "异常", itemStyle: { color: "#FFA500" } },
];

// 通用饼图配置
const getChartOption = (data) => {
  return {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c}% ({d}%)",
    },
    series: [
      {
        name: "统计",
        type: "pie",
        radius: ["40%", "70%"], // 环形饼图
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: data,
      },
    ],
  };
};

// 初始化图表
const initCharts = () => {
  // 初始化在线率图表
  if (onlineChart.value) {
    onlineChartInstance = echarts.init(onlineChart.value);
    onlineChartInstance.setOption(getChartOption(onlineData));
  }

  // 初始化画面正常率图表
  if (normalChart.value) {
    normalChartInstance = echarts.init(normalChart.value);
    normalChartInstance.setOption(getChartOption(normalData));
  }
};

// 窗口大小改变时重新调整图表大小
const handleResize = () => {
  if (onlineChartInstance) {
    onlineChartInstance.resize();
  }
  if (normalChartInstance) {
    normalChartInstance.resize();
  }
};

onMounted(() => {
  initCharts();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (onlineChartInstance) {
    onlineChartInstance.dispose();
  }
  if (normalChartInstance) {
    normalChartInstance.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.demo-charts-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.chart-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-container {
  width: 200px;
  height: 200px;
  margin-bottom: 20px;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  max-width: 200px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-dot.online,
.legend-dot.normal {
  background-color: #20b2aa;
}

.legend-dot.offline,
.legend-dot.abnormal {
  background-color: #ffa500;
}

.legend-text {
  font-size: 14px;
  color: #666;
  flex: 1;
}

.legend-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .chart-item {
    padding: 15px;
  }

  .chart-container {
    width: 150px;
    height: 150px;
  }
}
</style>
