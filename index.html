<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Vue3 + JsPlumb 链路图与缩略图</title>

  <!-- 使用更可靠的 CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.5/js/jsplumb.min.js"
    integrity="sha512-cQ+z0/xfVXwvApP+RHPEc5+YzuQX958uP8J7R7J0M7yNblpIDKuRR/W3J4+7mKk8FJuhT3k4QKslx2vCdEzU+g=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>

  <!-- 备用 CDN，如果上面的失败 -->
  <script>
    // 检查是否已加载 jsPlumb
    if (typeof jsPlumb === 'undefined') {
      console.log('使用备用 CDN');
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/jsplumb@2.15.6/dist/js/jsplumb.min.js';
      document.head.appendChild(script);
    }
  </script>

  <!-- 全局样式覆盖，确保缩略图显示在最上层 -->
  <style>
    .miniview-container {
      z-index: 10000 !important;
    }

    .miniview-canvas {
      z-index: 10001 !important;
    }

    /* 强制覆盖一些 jsPlumb 样式 */
    .jtk-connector {
      z-index: 4 !important;
    }

    .jtk-endpoint {
      z-index: 5 !important;
    }

    .jtk-overlay {
      z-index: 6 !important;
    }
  </style>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>