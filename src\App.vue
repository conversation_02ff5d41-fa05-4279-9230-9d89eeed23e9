<script setup>
import { ref } from "vue";
import NetworkDiagram from "./components/NetworkDiagram.vue";
import NetworkDiagramAlt from "./components/NetworkDiagramAlt.vue";

// 是否使用备选实现
const useAltImpl = ref(false);

// 节点数据
const nodes = ref([
  { id: 'node1', label: '节点1', x: 100, y: 100 },
  { id: 'node2', label: '节点2', x: 350, y: 100 },
  { id: 'node3', label: '节点3', x: 100, y: 300 },
  { id: 'node4', label: '节点4', x: 350, y: 300 },
  { id: 'node5', label: '节点5', x: 600, y: 200 }
]);

// 连接数据
const connections = ref([
  { source: 'node1', target: 'node2', label: '连接1' },
  { source: 'node2', target: 'node5', label: '连接2' },
  { source: 'node1', target: 'node3', label: '连接3' },
  { source: 'node3', target: 'node4', label: '连接4' },
  { source: 'node4', target: 'node5', label: '连接5' }
]);

// 切换实现方式
const toggleImpl = () => {
  useAltImpl.value = !useAltImpl.value;
};

// 处理节点点击
const handleNodeClick = (nodeId) => {
  console.log('点击了节点:', nodeId);
};
</script>

<template>
  <nav>
      <router-link to="/">首页</router-link> |
      <router-link to="/node-link">节点连线</router-link>
    </nav>
    <router-view />
</template>

<style scoped>
.app-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  text-align: center;
}

.title {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
}

.controls {
  margin-bottom: 20px;
}

.toggle-btn {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.toggle-btn:hover {
  background-color: #45a049;
}

.network-container {
  position: relative;
  width: 100%;
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  background-color: white;
}
</style>