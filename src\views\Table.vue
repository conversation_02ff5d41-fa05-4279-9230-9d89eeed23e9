<template>
    <el-table
      ref="multipleTableRef"
      :data="tableData"
      style="width: 100%"
      row-key="id"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <!-- 表格列定义 -->
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="姓名" />
      <!-- 其他列... -->
    </el-table>
    
    <el-pagination 
      @current-change="handlePageChange"
      :current-page="currentPage"
      :page-size="pageSize" 
      :total="total">
    </el-pagination>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const tableData = ref([]);
  const selectedRows = ref(new Set());
  const multipleTableRef = ref();
  
  // 加载表格数据
  const loadTableData = async () => {
    const res = await fetchData(); // 替换为您的API调用
    tableData.value = res.data;
    total.value = res.total;
  };
  
  // 分页切换
  const handlePageChange = async (newPage) => {
    currentPage.value = newPage;
    await loadTableData();
    // 恢复选中状态
    tableData.value.forEach(row => {
      if (selectedRows.value.has(row.id)) {
        multipleTableRef.value.toggleRowSelection(row, true);
      }
    });
  };
  
  // 单个选中处理
  const handleSelect = (selection, row) => {
    selection.includes(row) 
      ? selectedRows.value.add(row.id)
      : selectedRows.value.delete(row.id);
  };
  
  // 全选处理
  const handleSelectAll = (selection) => {
    selection.length === 0
      ? tableData.value.forEach(row => selectedRows.value.delete(row.id))
      : tableData.value.forEach(row => selectedRows.value.add(row.id));
  };
  </script>