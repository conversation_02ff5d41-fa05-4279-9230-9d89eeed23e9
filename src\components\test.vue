const option = {
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%']
      // ... 其他配置
    }
  ],
  graphic: (() => {
    // 1. 获取圆环的配置
    const radius = ['40%', '70%']
    const center = ['50%', '50%']
    
    // 2. 计算实际的半径和中心点位置（转换百分比为小数）
    const innerRadius = parseFloat(radius[0]) / 100  // 0.4
    const outerRadius = parseFloat(radius[1]) / 100  // 0.7
    const chartCenter = center.map(pos => parseFloat(pos) / 100)  // [0.5, 0.5]
    
    // 3. 计算文字位置和大小
    const containerWidth = 600  // 图表容器宽度
    const containerHeight = 400 // 图表容器高度
    
    // 4. 计算实际像素值
    const centerX = containerWidth * chartCenter[0]  // 300
    const centerY = containerHeight * chartCenter[1] // 200
    const innerRadiusPixels = containerWidth * innerRadius // 240
    
    return {
      type: 'group',
      // 使用计算后的位置
      left: centerX,
      top: centerY,
      children: [
        {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '总计',
            textAlign: 'center',
            fill: '#999',
            fontSize: Math.floor(innerRadiusPixels * 0.3), // 根据内圆半径计算字体大小
            y: -innerRadiusPixels * 0.1
          }
        },
        {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '1000',
            textAlign: 'center',
            fill: '#333',
            fontSize: Math.floor(innerRadiusPixels * 0.4),
            fontWeight: 'bold',
            y: innerRadiusPixels * 0.1
          }
        }
      ]
    }
  })()
}