<template>
  <div ref="miniviewContainer" class="miniview-component"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watchEffect } from 'vue';
import MiniViewPlugin from '../utils/MiniViewPlugin';

const props = defineProps({
  jsPlumbInstance: {
    type: Object,
    required: true
  },
  width: {
    type: Number,
    default: 200
  },
  height: {
    type: Number,
    default: 150
  },
  scale: {
    type: Number,
    default: 0.15
  },
  showConnectionLines: {
    type: Boolean,
    default: true
  }
});

const miniviewContainer = ref(null);
let miniviewPlugin = null;
let updateInterval = null;

onMounted(() => {
  initMiniview();
  
  // 设置定时更新，确保缩略图不会消失
  updateInterval = setInterval(() => {
    if (miniviewPlugin) {
      miniviewPlugin.update();
    }
  }, 3000);
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
  
  if (miniviewPlugin) {
    miniviewPlugin.destroy();
  }
});

// 监听 jsPlumbInstance 的变化
watchEffect(() => {
  if (props.jsPlumbInstance && miniviewContainer.value && !miniviewPlugin) {
    initMiniview();
  }
});

const initMiniview = () => {
  if (!props.jsPlumbInstance || !miniviewContainer.value) {
    console.error('缺少必要的参数或DOM元素');
    return;
  }
  
  // 延迟初始化，确保 DOM 已完全渲染
  setTimeout(() => {
    console.log('MiniviewComponent: 初始化缩略图');
    
    try {
      miniviewPlugin = new MiniViewPlugin(props.jsPlumbInstance, {
        container: miniviewContainer.value,
        width: props.width,
        height: props.height,
        surfaceScale: props.scale,
        showConnectionLines: props.showConnectionLines,
        elementFilter: (el) => el.classList.contains('node')
      });
      
      // 手动触发首次更新
      miniviewPlugin.update();
    } catch (error) {
      console.error('初始化缩略图插件失败:', error);
    }
  }, 500);
};
</script>

<style scoped>
.miniview-component {
  /* 移除相对定位，使用绝对定位 */
  position: absolute;
  top: 10px;
  right: 10px;
  width: v-bind('props.width + "px"');
  height: v-bind('props.height + "px"');
  border: 1px solid #ccc;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999 !important;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  pointer-events: none;
}
</style>