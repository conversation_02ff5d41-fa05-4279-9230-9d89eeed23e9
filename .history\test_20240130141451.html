<!DOCTYPE html>
<html lang="en">
<head>
  <title></title>
  <style>
    body{
        display: block;
        margin: 8px;
    }
    #diagramContainer{
        padding: 20px;
        width: 80%;
        height: 200px;
        border: 1px solid gray;
    }
    .item{
        height: 80px;
        width: 80px;
        border: 1px solid blue;
        position: absolute;
    }
  </style>
</head>
<body>
    <div id="diagramContainer">
        <div id="item_left" class="item"></div>
        <div id="item_right" class="item" style="left:250px;"></div>
    </div>
</body>

</html>
<script src="./jsplumb.min.js"></script>
<script>
    /* global jsPlumb */
    jsPlumb.ready(function () {
        var common = {
            endpoint: 'Rectangle',
            connector: ['Bezier'],
            anchor: ['Left', 'Right']
        }
        jsPlumb.connect({
        source: 'item_left',
        target: 'item_right',
        paintStyle: { stroke: 'lightgray', strokeWidth: 3 },
        endpointStyle: { fill: 'red', outlineStroke: 'darkgray', outlineWidth: 2 },
        overlays: [ ['Arrow', { width: 12, length: 12, location: 0.5 }],[ "Label",{ label:"<span>关联2</span><span style='color: red'>2323</span>",cssClass:"csslabel"} ], ]
        }, common)
        
    })// 创建一个匹配rgba的正则表达式

    let originalString = "rgba(255, 0, 0, 1)"
    const regex = /rgba\((\d+,\s*\d+,\s*\d+,\s*)[0-9.]+\)/g;

// 替换匹配到的rgba中的a为新值，例如0.5
const modifiedString = 'rgba(' + originalString.replace(regex, '$1' + '0' + ')');
console.log(modifiedString); // 输出：rgba(255, 0, 0, 0.5)

    // jsPlumb.ready(function () {
    //     jsPlumb.setContainer('diagramContainer')

    //     var common = {
    //         isSource: true,
    //         isTarget: true,
    //         connector: ['Straight'],
    //         endpoint: 'Dot',
    //         paintStyle: {
    //         fill: 'white',
    //         outlineStroke: 'blue',
    //         strokeWidth: 3
    //         },
    //         connectorStyle: {
    //             outlineStroke: 'green',
    //             strokeWidth: 1
    //         },
    //         connectorHoverStyle: {
    //             strokeWidth: 2
    //         }
    //     }

    //     jsPlumb.addEndpoint('item_left', {
    //         anchors: ['Right']
    //     }, common)

    //     jsPlumb.addEndpoint('item_right', {
    //         anchor: 'Left'
    //     }, common)

    //     jsPlumb.addEndpoint('item_right', {
    //         anchor: 'Right'
    //     }, common)
    // })
</script>