/**
 * 流程图编辑器小地图插件
 * 提供整个流程图的缩略预览和导航功能
 */

export default class MiniViewPlugin {
    /**
     * 创建小地图插件
     * @param {Object} editor 编辑器实例
     * @param {Object} options 配置选项
     * @param {HTMLElement} options.container 小地图容器元素
     * @param {number} options.width 小地图宽度，默认200
     * @param {number} options.height 小地图高度，默认150
     * @param {boolean} options.autoUpdate 是否自动更新，默认true
     * @param {number} options.updateInterval 自动更新间隔(ms)，默认1000
     */
    constructor(editor, options = {}) {
      this.editor = editor;
      this.container = options.container || null;
      this.width = options.width || 200;
      this.height = options.height || 150;
      this.autoUpdate = options.autoUpdate !== false;
      this.updateInterval = options.updateInterval || 1000;
      
      // 内部状态
      this.nodes = [];
      this.transform = null;
      this.bounds = null;
      this.viewport = null;
      this.scale = 1;
      this.activeNode = null;
      this.isDragging = false;
      this.mousePosition = { x: 0, y: 0 };
      
      // 绑定方法到实例
      this.handleMouseDown = this.handleMouseDown.bind(this);
      this.handleMouseMove = this.handleMouseMove.bind(this);
      this.handleMouseUp = this.handleMouseUp.bind(this);
      this.updateViewport = this.updateViewport.bind(this);
      this.update = this.update.bind(this);
      
      // 初始化小地图
      this.init();
    }
    
    /**
     * 初始化小地图
     */
    init() {
      if (!this.container) {
        console.error('MiniViewPlugin: 缺少容器元素');
        return;
      }
      
      // 创建画布
      this.canvas = document.createElement('canvas');
      this.canvas.width = this.width;
      this.canvas.height = this.height;
      this.context = this.canvas.getContext('2d');
      
      // 设置容器样式
      this.container.style.width = `${this.width}px`;
      this.container.style.height = `${this.height}px`;
      this.container.style.overflow = 'hidden';
      this.container.style.position = 'relative';
      this.container.style.backgroundColor = '#f5f5f5';
      this.container.style.border = '1px solid #ddd';
      this.container.style.borderRadius = '4px';
      
      // 添加画布到容器
      this.container.appendChild(this.canvas);
      
      // 分析编辑器结构
      this.logEditorStructure();
      
      // 初始化事件监听
      this.setupEvents();
      
      // 设置更新定时器
      if (this.autoUpdate) {
        this.timer = setInterval(this.update, this.updateInterval);
      }
      
      // 首次更新
      this.update();
    }
    
    /**
     * 记录编辑器结构以便调试
     */
    logEditorStructure() {
      if (!this.editor) {
        console.log('编辑器对象不存在');
        return;
      }
      
      console.group('编辑器结构分析');
      
      // 记录顶级属性
      console.log('顶级属性:', Object.keys(this.editor));
      
      // 检查常见的节点集合属性
      ['nodes', 'elements', 'components', 'children'].forEach(prop => {
        if (this.editor[prop]) {
          console.log(`编辑器.${prop}:`, this.editor[prop]);
        }
      });
      
      // 检查视图相关属性
      if (this.editor.view) {
        console.log('视图属性:', Object.keys(this.editor.view));
        
        if (this.editor.view.container) {
          console.log('容器子元素数量:', this.editor.view.container.children.length);
        }
      }
      
      // 检查常见的方法
      ['getNodes', 'getElements', 'getComponents'].forEach(method => {
        if (typeof this.editor[method] === 'function') {
          try {
            const result = this.editor[method]();
            console.log(`${method}() 结果:`, result);
          } catch (error) {
            console.log(`调用 ${method}() 出错:`, error);
          }
        }
      });
      
      console.groupEnd();
    }
    
    /**
     * 设置事件监听
     */
    setupEvents() {
      this.canvas.addEventListener('mousedown', this.handleMouseDown);
      document.addEventListener('mousemove', this.handleMouseMove);
      document.addEventListener('mouseup', this.handleMouseUp);
      
      // 监听编辑器变化
      if (this.editor && this.editor.on) {
        this.editor.on('nodetranslated', this.update);
        this.editor.on('noderemoved', this.update);
        this.editor.on('nodeadded', this.update);
        this.editor.on('translatenode', this.update);
        this.editor.on('zoom', this.update);
        this.editor.on('translate', this.update);
        
        // 尝试监听更多可能的事件
        ['change', 'process', 'nodecreated', 'connectioncreated'].forEach(event => {
          try {
            this.editor.on(event, this.update);
          } catch (error) {
            // 忽略不支持的事件
          }
        });
      }
    }
    
    /**
     * 移除事件监听
     */
    removeEvents() {
      this.canvas.removeEventListener('mousedown', this.handleMouseDown);
      document.removeEventListener('mousemove', this.handleMouseMove);
      document.removeEventListener('mouseup', this.handleMouseUp);
      
      // 移除编辑器事件监听
      if (this.editor && this.editor.off) {
        // 移除基本事件
        const events = [
          'nodetranslated', 'noderemoved', 'nodeadded', 
          'translatenode', 'zoom', 'translate',
          'change', 'process', 'nodecreated', 'connectioncreated'
        ];
        
        events.forEach(event => {
          try {
            this.editor.off(event, this.update);
          } catch (error) {
            // 忽略不支持的事件
          }
        });
      }
    }
    
    /**
     * 处理鼠标按下事件
     * @param {MouseEvent} e 鼠标事件
     */
    handleMouseDown(e) {
      const rect = this.canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // 检查是否点击在视口内
      if (this.viewport && 
          x >= this.viewport.x && 
          x <= this.viewport.x + this.viewport.width && 
          y >= this.viewport.y && 
          y <= this.viewport.y + this.viewport.height) {
        this.isDragging = true;
        this.mousePosition = { x, y };
        e.preventDefault(); // 防止选择文本
      }
    }
    
    /**
     * 处理鼠标移动事件
     * @param {MouseEvent} e 鼠标事件
     */
    handleMouseMove(e) {
      if (!this.isDragging) return;
      
      const rect = this.canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // 计算移动距离
      const dx = x - this.mousePosition.x;
      const dy = y - this.mousePosition.y;
      
      // 更新鼠标位置
      this.mousePosition = { x, y };
      
      // 移动视口
      this.updateViewport(dx, dy);
      e.preventDefault(); // 防止选择文本
    }
    
    /**
     * 处理鼠标释放事件
     */
    handleMouseUp() {
      this.isDragging = false;
    }
    
    /**
     * 更新编辑器视口位置
     * @param {number} dx X轴移动距离
     * @param {number} dy Y轴移动距离
     */
    updateViewport(dx, dy) {
      if (!this.editor || !this.bounds || !this.scale) return;
      
      // 计算实际移动距离（在流程图坐标系中）
      const realDx = dx / this.scale;
      const realDy = dy / this.scale;
      
      // 更新编辑器视图位置
      if (this.editor.view && this.editor.view.area) {
        const { k, x, y } = this.editor.view.area.transform;
        this.editor.view.area.transform = { 
          k, 
          x: x - realDx * k, 
          y: y - realDy * k 
        };
        
        // 触发更新
        if (this.editor.view.update) {
          this.editor.view.update();
        }
      } else if (this.editor.updateViewport) {
        // 尝试使用可能存在的updateViewport方法
        this.editor.updateViewport(-realDx, -realDy);
      }
    }
    
    /**
     * 更新小地图
     */
    update() {
      if (!this.editor) return;
      
      this.clear();
      
      // 添加详细的调试信息
      console.log('MiniViewPlugin 更新 - 编辑器对象:', this.editor);
      
      // 尝试多种方式获取节点
      // 1. 直接访问 nodes 属性
      if (this.editor.nodes && typeof this.editor.nodes[Symbol.iterator] === 'function') {
        this.nodes = Array.from(this.editor.nodes);
        console.log('成功: 通过 editor.nodes 获取节点，找到', this.nodes.length, '个节点');
      } 
      // 2. 尝试 getNodes 方法
      else if (this.editor.getNodes && typeof this.editor.getNodes === 'function') {
        this.nodes = this.editor.getNodes() || [];
        console.log('成功: 通过 editor.getNodes() 获取节点，找到', this.nodes.length, '个节点');
      }
      // 3. 尝试 getElements 方法
      else if (this.editor.getElements && typeof this.editor.getElements === 'function') {
        this.nodes = this.editor.getElements() || [];
        console.log('成功: 通过 editor.getElements() 获取节点，找到', this.nodes.length, '个节点');
      }
      // 4. 尝试从DOM中获取节点
      else if (this.editor.view && this.editor.view.container) {
        // 从DOM元素中获取节点
        try {
          const container = this.editor.view.container;
          // 查找具有特定类或数据属性的节点元素
          const nodeElements = container.querySelectorAll('.node, [data-type="node"], [data-node-id], .flowchart-node, .workflow-node');
          
          if (nodeElements.length > 0) {
            this.nodes = Array.from(nodeElements).map(el => {
              // 从DOM元素中提取节点信息
              const rect = el.getBoundingClientRect();
              const containerRect = container.getBoundingClientRect();
              
              // 计算相对于容器的位置
              const position = {
                x: rect.left - containerRect.left + container.scrollLeft,
                y: rect.top - containerRect.top + container.scrollTop
              };
              
              return {
                id: el.id || el.getAttribute('data-node-id') || String(Math.random()),
                meta: {
                  position: position,
                  width: rect.width,
                  height: rect.height
                },
                type: el.getAttribute('data-type') || 'default',
                selected: el.classList.contains('selected')
              };
            });
            console.log('成功: 从DOM元素中获取节点，找到', this.nodes.length, '个节点');
          } else {
            this.nodes = [];
            console.warn('MiniViewPlugin: 在DOM中未找到节点元素');
          }
        } catch (error) {
          console.error('从DOM获取节点时出错:', error);
          this.nodes = [];
        }
      }
      // 5. 如果以上都失败
      else {
        this.nodes = [];
        console.warn('MiniViewPlugin: 编辑器中没有可用的节点集合 - 请检查编辑器的API或结构');
        // 打印编辑器的所有属性，帮助识别可能的节点集合
        console.log('编辑器属性:', Object.keys(this.editor));
        if (this.editor.view) {
          console.log('编辑器视图属性:', Object.keys(this.editor.view));
        }
      }
      
      // 确保transform存在
      if (this.editor.view && this.editor.view.area && this.editor.view.area.transform) {
        this.transform = { ...this.editor.view.area.transform };
      } else if (this.editor.getTransform && typeof this.editor.getTransform === 'function') {
        this.transform = this.editor.getTransform();
      } else {
        this.transform = { k: 1, x: 0, y: 0 };
        console.warn('MiniViewPlugin: 无法获取编辑器的transform信息');
      }
      
      // 仅在有节点时执行绘制
      if (this.nodes.length > 0) {
        this.calculateBounds();
        this.drawNodes();
      } else {
        // 设置默认边界
        this.bounds = { x: 0, y: 0, width: this.canvas.width, height: this.canvas.height };
        this.scale = 1;
      }
      
      this.drawViewport();
      
      // 如果有连接方法，绘制连接
      if (typeof this.drawConnections === 'function' && this.nodes.length > 1) {
        this.drawConnections();
      }
    }
    
    /**
     * 计算节点的边界范围
     */
    calculateBounds() {
      if (!this.nodes.length) {
        this.bounds = { x: 0, y: 0, width: 10, height: 10 };
        return;
      }
    
      try {
        let minX = Infinity;
        let minY = Infinity;
        let maxX = -Infinity;
        let maxY = -Infinity;
        
        // 计算所有节点的边界
        this.nodes.forEach(node => {
          // 确保node.meta存在，如果不存在则使用合理的默认值
          const meta = node.meta || {};
          const position = meta.position || node.position || { x: 0, y: 0 };
          const width = meta.width || node.width || 200;
          const height = meta.height || node.height || 100;
          
          minX = Math.min(minX, position.x);
          minY = Math.min(minY, position.y);
          maxX = Math.max(maxX, position.x + width);
          maxY = Math.max(maxY, position.y + height);
        });
        
        // 添加边距
        const padding = 50;
        this.bounds = {
          x: minX - padding,
          y: minY - padding,
          width: maxX - minX + padding * 2,
          height: maxY - minY + padding * 2
        };
        
        // 计算缩放比例以适应画布
        const scaleX = this.canvas.width / this.bounds.width;
        const scaleY = this.canvas.height / this.bounds.height;
        this.scale = Math.min(scaleX, scaleY, 1); // 限制最大缩放为1
      } catch (error) {
        console.error('计算边界时出错:', error);
        this.bounds = { x: 0, y: 0, width: this.canvas.width, height: this.canvas.height };
        this.scale = 1;
      }
    }
    
    /**
     * 绘制节点
     */
    drawNodes() {
      if (!this.context || !this.bounds || !this.scale) return;
      
      this.context.save();
      
      this.nodes.forEach(node => {
        try {
          // 更健壮的属性访问
          const meta = node.meta || {};
          const position = meta.position || node.position || { x: 0, y: 0 };
          const width = meta.width || node.width || 200;
          const height = meta.height || node.height || 100;
          
          // 将节点位置从图表坐标转换为小地图坐标
          const x = (position.x - this.bounds.x) * this.scale;
          const y = (position.y - this.bounds.y) * this.scale;
          const w = width * this.scale;
          const h = height * this.scale;
          
          // 绘制节点
          this.context.fillStyle = this.getNodeColor(node);
          this.context.strokeStyle = '#666';
          this.context.lineWidth = 1;
          this.context.fillRect(x, y, w, h);
          this.context.strokeRect(x, y, w, h);
        } catch (error) {
          console.error('绘制节点时出错:', error, node);
        }
      });
      
      this.context.restore();
    }
    
    /**
     * 获取节点颜色
     * @param {Object} node 节点对象
     * @returns {string} 颜色值
     */
    getNodeColor(node) {
      // 根据节点类型或状态设置颜色
      if (node === this.activeNode) return '#ff7f0e';
      
      const type = (node.type || node.nodeType || '').toLowerCase();
      const defaultColor = '#1f77b4';
      
      const colorMap = {
        'input': '#d9f7be',
        'output': '#ffccc7',
        'default': '#e6f7ff',
        'process': '#fff0f6',
        'decision': '#f9f0ff',
        'start': '#d9f7be',
        'end': '#ffccc7',
        'task': '#e6f7ff',
        'custom': '#fff0f6'
      };
      
      // 如果节点被选中，返回不同的颜色
      if (node.selected) {
        return '#d6e4ff';
      }
      
      return colorMap[type] || defaultColor;
    }
    
    /**
     * 绘制视口
     */
    drawViewport() {
      if (!this.context || !this.editor || !this.bounds || !this.scale) return;
      
      // 确保transform存在
      if (!this.editor.view || !this.editor.view.area || !this.editor.view.area.transform) {
        console.warn('MiniViewPlugin: 无法获取编辑器视图区域的transform');
        return;
      }
      
      const { k, x, y } = this.editor.view.area.transform;
      
      // 确保container存在
      if (!this.editor.view.container) {
        console.warn('MiniViewPlugin: 无法获取编辑器视图容器');
        return;
      }
      
      const rect = this.editor.view.container.getBoundingClientRect();
      const screenWidth = rect.width;
      const screenHeight = rect.height;
      
      // 计算视口在小地图上的位置和大小
      const vpX = (-x / k - this.bounds.x) * this.scale;
      const vpY = (-y / k - this.bounds.y) * this.scale;
      const vpWidth = (screenWidth / k) * this.scale;
      const vpHeight = (screenHeight / k) * this.scale;
      
      // 保存视口信息
      this.viewport = { x: vpX, y: vpY, width: vpWidth, height: vpHeight };
      
      // 绘制视口
      this.context.save();
      this.context.strokeStyle = 'rgba(255, 0, 0, 0.8)';
      this.context.lineWidth = 2;
      this.context.strokeRect(vpX, vpY, vpWidth, vpHeight);
      
      // 半透明填充
      this.context.fillStyle = 'rgba(255, 0, 0, 0.1)';
      this.context.fillRect(vpX, vpY, vpWidth, vpHeight);
      this.context.restore();
    }
    
    /**
     * 绘制连接线
     */
    drawConnections() {
      if (!this.context || !this.bounds || !this.scale) return;
      
      let connections = [];
      
      // 尝试多种方式获取连接
      if (this.editor.getConnections && typeof this.editor.getConnections === 'function') {
        connections = this.editor.getConnections() || [];
      } else if (this.editor.connections && typeof this.editor.connections[Symbol.iterator] === 'function') {
        connections = Array.from(this.editor.connections);
      } else if (this.editor.edges && typeof this.editor.edges[Symbol.iterator] === 'function') {
        connections = Array.from(this.editor.edges);
      }
      
      if (connections.length === 0) return;
      
      this.context.save();
      this.context.strokeStyle = '#999';
      this.context.lineWidth = 1;
      
      connections.forEach(conn => {
        try {
          const sourceId = conn.source || conn.sourceId || conn.from;
          const targetId = conn.target || conn.targetId || conn.to;
          
          if (!sourceId || !targetId) return;
          
          const sourceNode = this.nodes.find(n => n.id === sourceId);
          const targetNode = this.nodes.find(n => n.id === targetId);
          
          if (!sourceNode || !targetNode) return;
          
          // 获取节点位置和尺寸
          const sourceMeta = sourceNode.meta || {};
          const targetMeta = targetNode.meta || {};
          
          const sourcePos = sourceMeta.position || sourceNode.position || { x: 0, y: 0 };
          const sourceWidth = sourceMeta.width || sourceNode.width || 200;
          const sourceHeight = sourceMeta.height || sourceNode.height || 100;
          
          const targetPos = targetMeta.position || targetNode.position || { x: 0, y: 0 };
          const targetHeight = targetMeta.height || targetNode.height || 100;
          
          // 计算连接点
          const sx = ((sourcePos.x + sourceWidth) - this.bounds.x) * this.scale;
          const sy = ((sourcePos.y + sourceHeight / 2) - this.bounds.y) * this.scale;
          const tx = ((targetPos.x) - this.bounds.x) * this.scale;
          const ty = ((targetPos.y + targetHeight / 2) - this.bounds.y) * this.scale;
          
          // 绘制连接线
          this.context.beginPath();
          this.context.moveTo(sx, sy);
          
          // 使用贝塞尔曲线
          const dx = Math.abs(tx - sx) * 0.5;
          this.context.bezierCurveTo(
            sx + dx, sy,
            tx - dx, ty,
            tx, ty
          );
          
          this.context.stroke();
          
          // 绘制箭头
          const arrowSize = 4;
          const angle = Math.atan2(ty - sy, tx - sx);
          
          this.context.beginPath();
          this.context.moveTo(tx, ty);
          this.context.lineTo(
            tx - arrowSize * Math.cos(angle - Math.PI / 6),
            ty - arrowSize * Math.sin(angle - Math.PI / 6)
          );
          this.context.lineTo(
            tx - arrowSize * Math.cos(angle + Math.PI / 6),
            ty - arrowSize * Math.sin(angle + Math.PI / 6)
          );
          this.context.closePath();
          this.context.fill();
        } catch (error) {
          console.error('绘制连接线时出错:', error);
        }
      });
      
      this.context.restore();
    }
    
    /**
     * 清除画布
     */
    clear() {
      if (this.context) {
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
      }
    }
    
    /**
     * 销毁插件，清理资源
     */
    destroy() {
      // 清除定时器
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      
      // 移除事件监听
      this.removeEvents();
      
      // 清空画布
      this.clear();
      
      // 移除画布
      if (this.canvas && this.canvas.parentNode) {
        this.canvas.parentNode.removeChild(this.canvas);
      }
      
      // 清空引用
      this.editor = null;
      this.container = null;
      this.context = null;
      this.canvas = null;
    }
  }